[2025-07-01T17:27:31.551Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:27:50.304Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:28:38.184Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:109:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:29:14.963Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:29:14.963Z"}
[2025-07-01T17:29:27.923Z] [INFO] GET / - 500 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"15ms","timestamp":"2025-07-01T17:29:27.923Z"}
[2025-07-01T17:29:28.391Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:29:28.394Z] [INFO] Process terminated
[2025-07-01T17:29:52.376Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:29:52.375Z"}
[2025-07-01T17:30:38.785Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:30:38.784Z"}
[2025-07-01T17:30:49.809Z] [INFO] GET / - 200 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:30:49.809Z"}
[2025-07-01T17:30:49.834Z] [INFO] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:30:49.834Z"}
[2025-07-01T17:30:49.838Z] [INFO] GET /css/style.css - 200 - 8ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:30:49.838Z"}
[2025-07-01T17:30:49.843Z] [INFO] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:49.843Z"}
[2025-07-01T17:30:50.084Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:30:50.088Z] [INFO] GET /api/check-student-auth - 404 - 7ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:30:50.088Z"}
[2025-07-01T17:30:50.100Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:30:50.103Z] [INFO] GET /student/login - 404 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:50.103Z"}
[2025-07-01T17:30:51.328Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:51.328Z"}
[2025-07-01T17:30:51.334Z] [WARN] 404 Not Found | {"url":"/api/auth/status","method":"GET","ip":"::1"}
[2025-07-01T17:30:51.337Z] [INFO] GET /api/auth/status - 404 - 4ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:51.337Z"}
[2025-07-01T17:30:51.341Z] [WARN] 404 Not Found | {"url":"/api/auth/status","method":"GET","ip":"::1"}
[2025-07-01T17:30:51.343Z] [INFO] GET /api/auth/status - 404 - 3ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:51.343Z"}
[2025-07-01T17:30:52.141Z] [INFO] GET / - 200 - 795ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"795ms","timestamp":"2025-07-01T17:30:52.141Z"}
[2025-07-01T17:30:52.144Z] [WARN] 404 Not Found | {"url":"/api/ratings","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.146Z] [INFO] GET /api/ratings - 404 - 2ms | {"method":"GET","url":"/api/ratings","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.146Z"}
[2025-07-01T17:30:52.149Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.151Z] [INFO] GET /favicon.ico - 404 - 3ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:52.151Z"}
[2025-07-01T17:30:52.154Z] [WARN] 404 Not Found | {"url":"/api/nonexistent","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.155Z] [INFO] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.155Z"}
[2025-07-01T17:34:12.538Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:34:12.538Z"}
[2025-07-01T17:34:12.544Z] [INFO] GET /check - 200 - 1ms | {"method":"GET","url":"/check","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.544Z"}
[2025-07-01T17:34:12.548Z] [INFO] GET /session - 200 - 1ms | {"method":"GET","url":"/session","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.548Z"}
[2025-07-01T17:34:13.122Z] [INFO] GET / - 200 - 571ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"571ms","timestamp":"2025-07-01T17:34:13.122Z"}
[2025-07-01T17:34:13.203Z] [INFO] GET /leaderboard - 200 - 79ms | {"method":"GET","url":"/leaderboard","ip":"::1","statusCode":200,"responseTime":"79ms","timestamp":"2025-07-01T17:34:13.203Z"}
[2025-07-01T17:34:13.206Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-01T17:34:13.208Z] [INFO] GET /favicon.ico - 404 - 2ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.208Z"}
[2025-07-01T17:34:13.211Z] [WARN] 404 Not Found | {"url":"/api/nonexistent","method":"GET","ip":"::1"}
[2025-07-01T17:34:13.212Z] [INFO] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.212Z"}
[2025-07-01T17:34:37.445Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:34:37.445Z"}
[2025-07-01T17:34:39.618Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:39.623Z] [INFO] GET /student/login - 404 - 11ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-01T17:34:39.623Z"}
[2025-07-01T17:34:44.597Z] [INFO] GET / - 304 - 10ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"10ms","timestamp":"2025-07-01T17:34:44.597Z"}
[2025-07-01T17:34:44.850Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:44.856Z] [INFO] GET /api/check-student-auth - 404 - 9ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"9ms","timestamp":"2025-07-01T17:34:44.856Z"}
[2025-07-01T17:34:44.875Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:44.880Z] [INFO] GET /student/login - 404 - 7ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:34:44.880Z"}
[2025-07-01T17:34:53.947Z] [INFO] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:34:53.947Z"}
[2025-07-01T17:34:54.091Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.093Z] [INFO] GET /api/check-student-auth - 404 - 3ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.093Z"}
[2025-07-01T17:34:54.098Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.099Z] [INFO] GET /student/login - 404 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.099Z"}
[2025-07-01T17:34:54.170Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.170Z"}
[2025-07-01T17:34:54.210Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.213Z] [INFO] GET /api/check-student-auth - 404 - 4ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:34:54.213Z"}
[2025-07-01T17:34:54.221Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.223Z] [INFO] GET /student/login - 404 - 3ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.223Z"}
