[2025-07-01T17:29:27.924Z] [ACCESS] GET / - 500 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"15ms","timestamp":"2025-07-01T17:29:27.923Z"}
[2025-07-01T17:30:49.810Z] [ACCESS] GET / - 200 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:30:49.809Z"}
[2025-07-01T17:30:49.836Z] [ACCESS] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:30:49.834Z"}
[2025-07-01T17:30:49.839Z] [ACCESS] GET /css/style.css - 200 - 8ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:30:49.838Z"}
[2025-07-01T17:30:49.843Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:49.843Z"}
[2025-07-01T17:30:50.090Z] [ACCESS] GET /api/check-student-auth - 404 - 7ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:30:50.088Z"}
[2025-07-01T17:30:50.105Z] [ACCESS] GET /student/login - 404 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:50.103Z"}
[2025-07-01T17:30:51.329Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:51.328Z"}
[2025-07-01T17:30:51.337Z] [ACCESS] GET /api/auth/status - 404 - 4ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:51.337Z"}
[2025-07-01T17:30:51.344Z] [ACCESS] GET /api/auth/status - 404 - 3ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:51.343Z"}
[2025-07-01T17:30:52.142Z] [ACCESS] GET / - 200 - 795ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"795ms","timestamp":"2025-07-01T17:30:52.141Z"}
[2025-07-01T17:30:52.146Z] [ACCESS] GET /api/ratings - 404 - 2ms | {"method":"GET","url":"/api/ratings","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.146Z"}
[2025-07-01T17:30:52.152Z] [ACCESS] GET /favicon.ico - 404 - 3ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:52.151Z"}
[2025-07-01T17:30:52.156Z] [ACCESS] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.155Z"}
[2025-07-01T17:34:12.539Z] [ACCESS] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:34:12.538Z"}
[2025-07-01T17:34:12.545Z] [ACCESS] GET /check - 200 - 1ms | {"method":"GET","url":"/check","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.544Z"}
[2025-07-01T17:34:12.548Z] [ACCESS] GET /session - 200 - 1ms | {"method":"GET","url":"/session","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.548Z"}
[2025-07-01T17:34:13.123Z] [ACCESS] GET / - 200 - 571ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"571ms","timestamp":"2025-07-01T17:34:13.122Z"}
[2025-07-01T17:34:13.204Z] [ACCESS] GET /leaderboard - 200 - 79ms | {"method":"GET","url":"/leaderboard","ip":"::1","statusCode":200,"responseTime":"79ms","timestamp":"2025-07-01T17:34:13.203Z"}
[2025-07-01T17:34:13.208Z] [ACCESS] GET /favicon.ico - 404 - 2ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.208Z"}
[2025-07-01T17:34:13.213Z] [ACCESS] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.212Z"}
[2025-07-01T17:34:39.624Z] [ACCESS] GET /student/login - 404 - 11ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-01T17:34:39.623Z"}
[2025-07-01T17:34:44.608Z] [ACCESS] GET / - 304 - 10ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"10ms","timestamp":"2025-07-01T17:34:44.597Z"}
[2025-07-01T17:34:44.857Z] [ACCESS] GET /api/check-student-auth - 404 - 9ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"9ms","timestamp":"2025-07-01T17:34:44.856Z"}
[2025-07-01T17:34:44.884Z] [ACCESS] GET /student/login - 404 - 7ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:34:44.880Z"}
[2025-07-01T17:34:53.947Z] [ACCESS] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:34:53.947Z"}
[2025-07-01T17:34:54.093Z] [ACCESS] GET /api/check-student-auth - 404 - 3ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.093Z"}
[2025-07-01T17:34:54.100Z] [ACCESS] GET /student/login - 404 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.099Z"}
[2025-07-01T17:34:54.172Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.170Z"}
[2025-07-01T17:34:54.214Z] [ACCESS] GET /api/check-student-auth - 404 - 4ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:34:54.213Z"}
[2025-07-01T17:34:54.224Z] [ACCESS] GET /student/login - 404 - 3ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.223Z"}
